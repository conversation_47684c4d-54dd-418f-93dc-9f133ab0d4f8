'use client';

import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import MultiStepForm from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import useToast from '@shared/uikit/Toast/useToast';
import { setAutoMovement } from '@shared/utils/api/jobs';
import getStepData from '@shared/utils/getStepData';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  useAutoMoveRequirementsStepOne,
  useAutoMoveRequirementsStepTwo,
  useAutoMoveRequirementsStepThree,
  TemplateFormData,
} from './hooks';

const AutoRejectRequirementsModal: React.FC = () => {
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const { t } = useTranslation();
  const toast = useToast();
  const [formData, setFormData] = useState<any>(undefined);
  const [templateFormValue, setTemplateFormValue] = useState<
    TemplateFormData | undefined
  >(undefined);

  const { mutateAsync: submitAutoMovement } = useMutation({
    mutationFn: setAutoMovement,
    onSuccess: () => {
      toast({
        type: 'success',
        icon: 'check-circle',
        title: t('success'),
        message: t('auto_movement_updated'),
      });
      handleClose();
    },
    onError: (error: any) => {
      toast({
        type: 'error',
        icon: 'times-circle',
        title: t('error'),
        message: error.response?.data?.defaultMessage || t('error_occurred'),
      });
    },
  });

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'autoReject',
    });
  };

  const handleFormDataChange = (newFormData: any) => {
    setFormData(newFormData);
  };

  const handleTemplateCreated = (newTemplateId: number) => {
    submitAutoMovement({
      pipelineId: pipelineId.toString(),
      data: { ...formData, templateId: newTemplateId },
    });
  };

  const handleTemplateSelected = (selectedTemplate: TemplateFormData) => {
    setTemplateFormValue(selectedTemplate);
  };

  const stepOneData = useAutoMoveRequirementsStepOne({
    onFormDataChange: handleFormDataChange,
  });

  const stepTwoData = useAutoMoveRequirementsStepTwo({
    onTemplateCreated: handleTemplateCreated,
    templateFormValue,
  });

  const stepThreeData = useAutoMoveRequirementsStepThree({
    formData,
    onTemplateSelected: handleTemplateSelected,
  });

  const steps = [...stepOneData, ...stepTwoData, ...stepThreeData];

  const onClose = () => closeMultiStepForm('automation');
  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  return (
    <MultiStepForm
      wide
      initialStep={0}
      initialValues={{}} // Empty initial values - form fields will be created dynamically
      showConfirm={false}
      enableReinitialize
      totalSteps={2}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="automation"
    />
  );
};

export default AutoRejectRequirementsModal;
