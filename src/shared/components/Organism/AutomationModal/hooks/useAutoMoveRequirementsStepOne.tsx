import React, { useState } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import AutoComplete from '@shared/uikit/AutoComplete';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Typography from '@shared/uikit/Typography';
import useMedia from '@shared/uikit/utils/useMedia';
import useTranslation from '@shared/utils/hooks/useTranslation';
import geoApi from 'shared/utils/api/geo';
import {
  useStageOptions,
  useActionOptions,
  useTypeOptions,
} from '../components';
import { TwoButtonFooter } from '@shared/components/Organism/MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import { AutomationStageType } from '../components/AutomationStageType';
import { PipelineInfo } from '@shared/types/pipelineProps';

interface ActionOption {
  value: string;
  label: string;
}

interface TypeOption {
  value: string;
  label: string;
}

interface RequirementBox {
  id: string;
  action: ActionOption | null;
  type: TypeOption | null;
  value: any;
}

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  getStepHeaderProps?: Exclude<
    MultiStepFormProps['getStepHeaderProps'],
    undefined
  >;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

interface UseAutoMoveRequirementsStepOneProps {
  onFormDataChange: (formData: any) => void;
}

export function useAutoMoveRequirementsStepOne({
  onFormDataChange,
}: UseAutoMoveRequirementsStepOneProps): SingleDataItem[] {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const actionOptions = useActionOptions();
  const typeOptions = useTypeOptions();
  const { data } = useMultiStepFormState('automation') as {
    data: PipelineInfo | undefined;
  };

  const [requirementBoxes, setRequirementBoxes] = useState<RequirementBox[]>(
    []
  );
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const conditionMatchItems = [
    {
      label: t('all_requirements_must_be_met'),
      value: 'ALL',
    },
    {
      label: t('one_requirement_can_be_met'),
      value: 'ANY',
    },
  ];
  const [selectedConditionMatch, setSelectedConditionMatch] = useState<{
    label: string;
    value: string;
  }>(conditionMatchItems[0]);

  const LOCATION_FIELD = {
    apiFunc: geoApi.searchPlace,
    cp: 'asyncAutoCompleteWithExtraParams',
    label: t('location'),
    name: 'locationWithExtraParams',
    autoComplete: 'nope',
    rightIconProps: {
      name: 'search',
    },
    params: {
      countryCode,
    },
    visibleRightIcon: true,
  };

  const handleAddBox = () => {
    setRequirementBoxes([
      ...requirementBoxes,
      {
        id: Date.now().toString(),
        action: null,
        type: null,
        value: null,
      },
    ]);
  };

  const handleRemoveBox = (id: string) => {
    setRequirementBoxes(requirementBoxes.filter((box) => box.id !== id));
  };

  const handleActionChange = (id: string, action: ActionOption) => {
    setRequirementBoxes(
      requirementBoxes.map((box) =>
        box.id === id ? { ...box, action, type: null, value: null } : box
      )
    );
  };

  const handleTypeChange = (id: string, type: TypeOption) => {
    setRequirementBoxes(
      requirementBoxes.map((box) =>
        box.id === id ? { ...box, type, value: null } : box
      )
    );
  };

  const getFieldsForAction = (actionValue: string) => {
    switch (actionValue) {
      case 'location':
        return [LOCATION_FIELD];
      case 'age':
        return [
          {
            label: t('min_age'),
            name: 'minAge',
            type: 'number',
            cp: 'input',
            inputProps: {
              placeholder: t('min_age'),
            },
          },
          {
            label: t('max_age'),
            name: 'maxAge',
            type: 'number',
            cp: 'input',
            inputProps: {
              placeholder: t('max_age'),
            },
            wrapStyle: 'mt-20',
          },
        ];
      case 'coverLetter':
        return [
          {
            label: t('cover_letter'),
            name: 'coverLetter',
            type: 'text',
            cp: 'input',
            inputProps: {
              placeholder: t('enter_cover_letter_text'),
            },
          },
        ];
      case 'phoneNumber':
        return [
          {
            label: t('phone_number'),
            name: 'phoneNumber',
            type: 'tel',
            cp: 'input',
            inputProps: {
              placeholder: t('enter_phone_number'),
            },
          },
        ];
      default:
        return [];
    }
  };

  const renderValueField = (box: RequirementBox, formikProps: any) => {
    if (!box.action || !box.type) return null;

    const fields = getFieldsForAction(box.action.value);
    if (fields.length === 0) return null;

    // Create field names that are unique to this requirement box
    const fieldsWithBoxId = fields.map((field) => ({
      ...field,
      name: `requirementBox_${box.id}_${field.name}`,
    }));

    return <DynamicFormBuilder groups={fieldsWithBoxId} />;
  };

  const transformFormData = (values: any) => {
    const ageBox = requirementBoxes.find((box) => box.action?.value === 'age');
    const locationBox = requirementBoxes.find(
      (box) => box.action?.value === 'location'
    );
    const coverLetterBox = requirementBoxes.find(
      (box) => box.action?.value === 'coverLetter'
    );
    const phoneNumberBox = requirementBoxes.find(
      (box) => box.action?.value === 'phoneNumber'
    );

    // Extract values from the MultiStepForm's Formik context
    const getBoxValue = (
      box: RequirementBox | undefined,
      fieldName: string
    ) => {
      if (!box) return undefined;
      return values[`requirementBox_${box.id}_${fieldName}`];
    };

    const formValue = {
      toPipelineId: parseInt(data?.id, 10),
      coverLetterCheckingEnabled: !!coverLetterBox,
      phoneNumberCheckingEnabled: !!phoneNumberBox,
      ageRangeCheckingEnabled: !!ageBox,
      ageRangeIsBetweenOrNot: ageBox?.type?.value === 'is',
      minAge: getBoxValue(ageBox, 'minAge')
        ? parseInt(getBoxValue(ageBox, 'minAge'), 10)
        : 0,
      maxAge: getBoxValue(ageBox, 'maxAge')
        ? parseInt(getBoxValue(ageBox, 'maxAge'), 10)
        : 0,
      selectedConditionMatch,
      locationCheckingEnabled: !!locationBox,
      locationCountryCodeIsEqualsOrNot: locationBox?.type?.value === 'is',
      locationCountryCodeIs:
        getBoxValue(locationBox, 'locationWithExtraParams')?.countryCode || '',
      // Store requirement boxes configuration for step persistence
      requirementBoxes,
      selectedConditionMatch,
    };

    return formValue;
  };

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('requirements'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => {
        closeMultiStepForm('automation');
        openMultiStepForm({
          formName: 'automation',
          data,
          type: 'autoMove',
        });
      },
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({
    setStep,
    values,
  }) => (
    <TwoButtonFooter
      submitLabel={t('next')}
      secondaryButtonLabel={t('discard')}
      onSubmitClick={() => {
        const formData = transformFormData(values);
        console.log('formData', formData);
        onFormDataChange(formData);
        setStep((prev) => prev + 1);
      }}
      disabledSubmit={
        !requirementBoxes?.some((box) => {
          // Check if any field for this box has a value
          const fields = getFieldsForAction(box.action?.value || '');
          return fields.some((field) => {
            const fieldName = `requirementBox_${box.id}_${field.name}`;
            return values?.[fieldName];
          });
        })
      }
      secondaryButtonOnClick={() => {
        closeMultiStepForm('automation');
      }}
    />
  );

  const getStepHeaderProps: SingleDataItem['getStepHeaderProps'] = () => ({
    title: t('move_to'),
    iconProps: {
      name: 'Pipe-move',
      type: 'fal',
    },
  });

  const renderBody: SingleDataItem['renderBody'] = ({
    values,
    setFieldValue,
  }) => (
    <Flex className="h-full bg-darkSecondary overflow-y-auto">
      <Flex className="gap-20">
        <Flex className="gap-20">
          <AutomationStageType type="autoMove" />

          <AutoComplete
            editable={false}
            visibleRightIcon
            variant="simple-large"
            value={selectedConditionMatch}
            onChangeInput={(value) => {
              const option = conditionMatchItems.find(
                (opt) => opt.value === value.value
              );
              if (option) {
                setSelectedConditionMatch(option);
              }
            }}
            inputWrapClassName="w-full"
            options={conditionMatchItems}
            renderItem={({ item }) => (
              <Flex
                flexDir="row"
                alignItems="center"
                className="w-full h-[56px] gap-10"
              >
                <Typography size={16}>{item.label}</Typography>
              </Flex>
            )}
            className="w-full"
            optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
            displayName={selectedConditionMatch.label}
            onSelect={(item) => {
              setSelectedConditionMatch(item);
            }}
          />
        </Flex>

        {!!requirementBoxes?.length && (
          <Flex className="gap-20">
            {requirementBoxes?.map((box) => (
              <Flex
                key={box.id}
                className="gap-20 bg-gray_5 p-20 border border-solid border-techGray_20 rounded-xl"
              >
                <Flex
                  flexDir="row"
                  className="justify-between items-center w-full"
                >
                  <Typography className="!text-xl !font-bold !text-smoke_coal">
                    {t('if')}
                  </Typography>
                  <IconButton
                    name="trash"
                    type="fas"
                    size="md20"
                    colorSchema="transparent"
                    iconProps={{
                      color: 'smoke_coal',
                    }}
                    onClick={() => handleRemoveBox(box.id)}
                    className=""
                  />
                </Flex>
                <Flex flexDir="column" className="gap-20">
                  <AutoComplete
                    editable={false}
                    visibleRightIcon
                    variant="simple-large"
                    placeholder={t('action')}
                    value={box.action ?? { value: '', label: '' }}
                    onSelect={(value: any) => handleActionChange(box.id, value)}
                    inputWrapClassName="w-full"
                    options={actionOptions}
                    rightIconClassName=""
                    className="w-full"
                    optionsVariant={
                      isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                    }
                  />
                  {box.action && (
                    <AutoComplete
                      editable={false}
                      visibleRightIcon
                      variant="simple-large"
                      placeholder={t('type')}
                      value={box.type ?? { label: '', value: '' }}
                      onSelect={(value: any) => handleTypeChange(box.id, value)}
                      inputWrapClassName="w-full"
                      options={typeOptions}
                      rightIconClassName=""
                      className="w-full"
                      optionsVariant={
                        isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                      }
                    />
                  )}
                  {box.action &&
                    box.type &&
                    renderValueField(box, { values, setFieldValue })}
                </Flex>
              </Flex>
            ))}
          </Flex>
        )}
        {requirementBoxes?.length <= 3 && (
          <Flex flexDir="row" className="justify-start">
            <IconButton
              name="plus"
              type="far"
              size="md"
              colorSchema="graySecondary"
              variant="circle"
              onClick={handleAddBox}
              tooltip={t('add_requirement')}
            />
          </Flex>
        )}
      </Flex>
    </Flex>
  );

  const stepData: Array<SingleDataItem> = [
    {
      stepKey: '1',
      getHeaderProps,
      getStepHeaderProps,
      renderBody,
      renderFooter,
    },
  ];

  return stepData;
}
